import { Transition } from '@headlessui/react';
import clsx from 'clsx';
import { useRef } from 'react';

const DTransitionFast = ({
  children,
  type = 'fade',
  show = false,
  className,
}) => {
  const contentRef = useRef(null);

  // Faster timing specifically for icons
  const time = 150;
  const delay = 25;
  
  const classesType = {
    [` relative overflow-hidden data-[enter]:duration-${time} data-[enter]:data-[closed]:overflow-hidden data-[enter]:data-[closed]:-translate-y-1`]:
      type === 'collapse',
    [`data-[leave]:delay-${delay} data-[leave]:duration-${time} data-[leave]:data-[closed]:-translate-y-1 data-[leave]:overflow-hidden`]:
      type === 'collapse',
    [`!max-h-[${contentRef.current?.clientHeight}px]`]:
      type === 'collapse' && show,
    'data-[closed]:opacity-0 data-[enter]:delay-25 duration-150':
      type === 'fade',
    'duration-150 data-[closed]:-translate-x-full top-1/2 -translate-y-1/2 absolute w-full':
      type === 'slide',
  };

  return (
    <>
      <Transition show={show} appear={true}>
        <div
          className={clsx(
            'transition-all data-[closed]:opacity-0',
            classesType,
            className
          )}
          ref={contentRef}
        >
          {children}
        </div>
      </Transition>
    </>
  );
};

export default DTransitionFast;