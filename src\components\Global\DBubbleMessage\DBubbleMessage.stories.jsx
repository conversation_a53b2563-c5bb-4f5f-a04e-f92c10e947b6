import { DANTE_ICON } from '@/constants';
import convertThemeToCSSVariablesStyle, {
  convertThemeToCSSVariablesObj,
} from '@/helpers/convertThemeToCSSVariables';
import { fn } from '@storybook/test';

import DDBubbleMessage from './index';
import { DateTime } from 'luxon';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories#default-export
export default {
  title: 'Chatbot/DBubbleMessage',
  component: DDBubbleMessage,
  parameters: {
    // Optional parameter to center the component in the Canvas. More info: https://storybook.js.org/docs/configure/story-layout
    layout: 'fullscreen',
    backgrounds: {
      default: 'white',
    },
  },
  // This component will have an automatically generated Autodocs entry: https://storybook.js.org/docs/writing-docs/autodocs
  tags: ['autodocs'],
  // More on argTypes: https://storybook.js.org/docs/api/argtypes
  argTypes: {
    role: {
      options: ['user', 'assistant', 'agent'],
      control: { type: 'radio' },
    },
    status: {
      options: ['complete', 'loading', 'typing', 'error'],
      control: { type: 'radio' },
    },
    type: {
      options: ['normal', 'welcome_message', 'flagged', 'info'],
      control: { type: 'radio' },
    },
    element: { control: 'color' },
    brand: { control: 'color' },
    surface: { control: 'color' },
    readonly: { control: 'boolean' },
    hideFooter: { control: 'boolean' },
    completed_date: { control: 'date' },
    reaction: {
      options: ['', 'thumbs_up', 'thumbs_down'],
      control: { type: 'radio' },
    },
  },
  // Use `fn` to spy on the onClick arg, which will appear in the actions panel once invoked: https://storybook.js.org/docs/essentials/actions#action-args
  args: {
    brand: '#8275F7',
    element: '#000000',
    surface: '#ffffff',
    image: DANTE_ICON,
    type: 'normal',
    status: 'complete',
    readonly: false,
    isInApp: false,
    agent_profile_pic: '',
    chatbot_profile_pic: '',
    isDanteFaq: false,
    completed_date: null,
    handleFooterButton: fn(),
  },
  render: (args) => {
    return (
      <div
        style={convertThemeToCSSVariablesObj({
          color: {
            surface: args.surface,
            element: args.element,
            brand: args.brand,
          },
        })}
      >
        <DDBubbleMessage {...args} />
      </div>
    );
  },
};

export const AssistantResponse = {
  args: {
    role: 'assistant',
    children: (
      <>
        This AI Chatbot contains information about Dante AI and its features. It
        includes details about creating and customizing AI Chatbots, training
        them, and integrating them with various platforms. The AI Chatbot also
        provides information on how to automate customer support using{' '}
        <a href="Dante AI">Dante AI</a>, including using the AI Chatbot, Zapier
        integration, and API access. Additionally, it covers topics such as
        sharing AI Chatbots, editing their personality, and uploading files and
        URLs. The AI Chatbot offers
      </>
    ),
  },
};

export const AgentResponse = {
  args: {
    role: 'agent',
    children: (
      <>
        This AI Chatbot contains information about Dante AI and its features. It
        includes details about creating and customizing AI Chatbots, training
        them, and integrating them with various platforms. The AI Chatbot also
        provides information on how to automate customer support using{' '}
        <a href="Dante AI">Dante AI</a>, including using the AI Chatbot, Zapier
        integration, and API access. Additionally, it covers topics such as
        sharing AI Chatbots, editing their personality, and uploading files and
        URLs. The AI Chatbot offers
      </>
    ),
  },
};

export const WelcomeMessage = {
  args: {
    role: 'assistant',
    type: 'welcome_message',
    children: <>👋 Hi, how can I help you?</>,
  },
};

export const UserQuestion = {
  args: {
    role: 'user',
    children: (
      <>
        Explain the contents of this chatbot. <a href="Dante AI">Dante AI</a>{' '}
        Explain the contents of this chatbot. Explain the contents of this
        chatbot. Explain the contents of this chatbot. Explain the contents of
        this chatbot. Explain the contents of this chatbot.{' '}
      </>
    ),
  },
};

export const UserSmallQuestion = {
  args: {
    role: 'user',
    children: <>Hi.</>,
  },
};

export const Loading = {
  args: {
    role: 'assistant',
    type: 'welcome_message',
    status: 'loading',
    children: <>👋 Hi, how can I help you?</>,
  },
};

export const IconTimingTest = {
  args: {
    role: 'assistant',
    status: 'complete',
    children: <>This is a test message to check icon timing. The sound and source icons should appear quickly after the message loads.</>,
    showSources: ['test-id'],
    id: 'test-id',
    isInApp: true,
    place: 'chat_record',
  },
  parameters: {
    docs: {
      description: {
        story: 'Test story to verify that sound and source icons appear quickly (25ms delay instead of 125ms).'
      }
    }
  }
};

export const AssistantInChatLog = {
  args: {
    role: 'assistant',
    completed_date: DateTime.now(),
    children: (
      <>
        This AI Chatbot contains information about Dante AI and its features. It
        includes details about creating and customizing AI Chatbots, training
        them, and integrating them with various platforms. The AI Chatbot also
        provides information on how to automate customer support using{' '}
        <a href="Dante AI">Dante AI</a>, including using the AI Chatbot, Zapier
        integration, and API access. Additionally, it covers topics such as
        sharing AI Chatbots, editing their personality, and uploading files and
        URLs. The AI Chatbot offers
      </>
    ),
  },
};

export const UserInChatLog = {
  args: {
    role: 'user',
    completed_date: DateTime.now(),
    children: (
      <>
        This AI Chatbot contains information about Dante AI and its features. It
        includes details about creating and customizing AI Chatbots, training
        them, and integrating them with various platforms. The AI Chatbot also
        provides information on how to automate customer support using{' '}
        <a href="Dante AI">Dante AI</a>, including using the AI Chatbot, Zapier
        integration, and API access. Additionally, it covers topics such as
        sharing AI Chatbots, editing their personality, and uploading files and
        URLs. The AI Chatbot offers
      </>
    ),
  },
};

export const InfoMessage = {
  args: {
    role: 'assistant',
    type: 'info',
    children: <>This is an info message.</>,
  },
};
