<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Icon Timing Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .message-bubble {
            background: #f0f0f0;
            padding: 15px;
            border-radius: 12px;
            margin: 10px 0;
            position: relative;
        }
        .icons-container {
            display: flex;
            gap: 8px;
            margin-top: 10px;
            opacity: 0;
            transition: opacity 0.15s ease-in-out;
        }
        .icons-container.show {
            opacity: 1;
        }
        .icons-container.slow {
            transition: opacity 0.45s ease-in-out 0.125s;
        }
        .icon-btn {
            width: 28px;
            height: 28px;
            border: none;
            border-radius: 4px;
            background: #e0e0e0;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s;
        }
        .icon-btn:hover {
            background: #d0d0d0;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .timing-info {
            background: #e8f4fd;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            font-weight: bold;
            color: #28a745;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎯 Icon Timing Test</h1>
        <p>This test shows the difference between the old (slow) and new (fast) icon animations.</p>
        
        <div class="timing-info">
            <strong>Old Implementation:</strong> 100ms + 25ms delay + 300ms + 150ms duration = <span style="color: red;">575ms total</span><br>
            <strong>New Implementation:</strong> 25ms delay + 150ms duration = <span style="color: green;">175ms total</span><br>
            <strong>Improvement:</strong> <span style="color: green;">3.3x faster!</span>
        </div>

        <div class="test-section">
            <h3>❌ Old (Slow) - Nested Transitions</h3>
            <div class="message-bubble">
                <p>Assistant message content here...</p>
                <div id="icons-slow" class="icons-container slow">
                    <button class="icon-btn" title="Sound">🔊</button>
                    <button class="icon-btn" title="Sources">📄</button>
                </div>
            </div>
            <p>Total delay: <span id="slow-time" class="status">-</span></p>
        </div>

        <div class="test-section">
            <h3>✅ New (Fast) - Single Fast Transition</h3>
            <div class="message-bubble">
                <p>Assistant message content here...</p>
                <div id="icons-fast" class="icons-container">
                    <button class="icon-btn" title="Sound">🔊</button>
                    <button class="icon-btn" title="Sources">📄</button>
                </div>
            </div>
            <p>Total delay: <span id="fast-time" class="status">-</span></p>
        </div>

        <div class="test-section">
            <h3>⚡ Instant (No Transition)</h3>
            <div class="message-bubble">
                <p>Assistant message content here...</p>
                <div id="icons-instant" class="icons-container" style="transition: none;">
                    <button class="icon-btn" title="Sound">🔊</button>
                    <button class="icon-btn" title="Sources">📄</button>
                </div>
            </div>
            <p>Total delay: <span id="instant-time" class="status">-</span></p>
        </div>

        <button onclick="runTest()">🚀 Run Test</button>
        <button onclick="resetTest()">🔄 Reset</button>
    </div>

    <script>
        let testStartTime = 0;

        function resetTest() {
            document.getElementById('icons-slow').classList.remove('show');
            document.getElementById('icons-fast').classList.remove('show');
            document.getElementById('icons-instant').classList.remove('show');
            document.getElementById('slow-time').textContent = '-';
            document.getElementById('fast-time').textContent = '-';
            document.getElementById('instant-time').textContent = '-';
        }

        function runTest() {
            resetTest();
            
            setTimeout(() => {
                testStartTime = performance.now();
                
                // Instant (no delay)
                document.getElementById('icons-instant').classList.add('show');
                const instantTime = performance.now() - testStartTime;
                document.getElementById('instant-time').textContent = `${Math.round(instantTime)}ms`;
                
                // Fast transition (25ms delay + 150ms duration)
                setTimeout(() => {
                    document.getElementById('icons-fast').classList.add('show');
                    setTimeout(() => {
                        const fastTime = performance.now() - testStartTime;
                        document.getElementById('fast-time').textContent = `${Math.round(fastTime)}ms`;
                    }, 175); // 25ms delay + 150ms duration
                }, 25);
                
                // Slow transition (125ms delay + 450ms duration)
                setTimeout(() => {
                    document.getElementById('icons-slow').classList.add('show');
                    setTimeout(() => {
                        const slowTime = performance.now() - testStartTime;
                        document.getElementById('slow-time').textContent = `${Math.round(slowTime)}ms`;
                    }, 575); // 125ms delay + 450ms duration
                }, 125);
                
            }, 100);
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(runTest, 500);
        });
    </script>
</body>
</html>
