import React, { useState } from 'react';
import { DateTime } from 'luxon';

import { DANTE_ICON } from '@/constants';

import DButtonIcon from '../DButtonIcon';
import DLoaderWriting from '../DLoaderWriting';
import ConfirmIcon from '../Icons/ConfirmIcon';
import SoundIcon from '../Icons/SoundIcon';
import ThumbDownIcon from '../Icons/ThumbDownIcon';
import ThumbUpIcon from '../Icons/ThumbUpIcon';
import clsx from 'clsx';
import DTransition from '../DTransition';
import FileIcon from '../Icons/FileIcon';
import DSpinner from '../DSpinner';
import FlagIcon from '../Icons/FlagIcon';
import QuickResponseIcon from '../Icons/QuickResponseIcon';
import AgentRequestIcon from '../Icons/AgentRequestIcon';
import PreviewImageModal from '../PreviewImageModal';
import featureCheck, { checkFeatureAvailability } from '@/helpers/tier/featureCheck';
import DTransitionFast from '../DTransitionFast/DTransitionFast';

const DBubbleMessage = ({
  id,
  role,
  children,
  status,
  chatbot_profile_pic,
  type,
  completed_date,
  readonly,
  hideFooter,
  reaction,
  agent_profile_pic,
  isInApp,
  handleFooterButton,
  isDanteFaq,
  sources,
  openSources,
  setOpenSources,
  showSources,
  sourcesLoading,
  chatImageLoader,
  was_answered,
  place = '',
  isInHumanHandoverApp = false,
  interactingWithLiveAgent,
  pollResponse,
  images = [],
  messageIndex = 0,
  kb_id,
}) => {
  const [previewImage, setPreviewImage] = useState(null);
  // Store the image source in a variable to ensure consistent rendering
  const profilePicSrc = (() => {
    // First check if we have an agent profile pic
    if (agent_profile_pic) {
      return agent_profile_pic instanceof File
        ? URL.createObjectURL(agent_profile_pic)
        : agent_profile_pic;
    }

    // If no agent pic, use the chatbot profile pic or default icon
    return chatbot_profile_pic instanceof File
      ? URL.createObjectURL(chatbot_profile_pic)
      : chatbot_profile_pic || DANTE_ICON;
  })();

  // Determine which animation to use based on the message index
  const animationClass = messageIndex < 3
    ? `animate-fadeInUpDelayed${messageIndex + 1}`
    : 'animate-fadeInUp';

  return (
    <>
      <PreviewImageModal
        isOpen={!!previewImage}
        onClose={() => setPreviewImage(null)}
        image={previewImage}
      />
      <article className={`flex w-full  ${animationClass} ${role === 'user' ? 'mb-size4' : ''} first:mb-size4`}>
      {role === 'user' ? (
        <h5 className="sr-only">You said: </h5>
      ) : (
        <h6 className="sr-only">Assistant said: </h6>
      )}

      {type !== 'info' && type !== 'agent_connecting' ? (
        <div className="text-sm md:text-base w-full ">
          <div
            className={clsx(
              'flex w-full gap-size1',
              role === 'user' ? 'justify-end' : 'justify-start',
              isInHumanHandoverApp && 'flex-row-reverse'
            )}
          >
            {role === 'assistant' &&  (kb_id !== 'ca7ac107-9716-4ef3-a6b5-55ceab49f15a' && kb_id !== '86927718-7690-4c0c-a99d-8bc8afda0a4c' && !isInApp) && (
              <div
                className="rounded-full size-6 flex items-center justify-center overflow-hidden"
                style={{ backgroundColor: 'var(--dt-color-brand-10)' }}
              >
                <img
                  src={profilePicSrc}
                  className="size-4"
                  alt=""
                />
              </div>
            )}
            {role === 'agent' && (
              <div className="rounded-full size-6 flex items-center justify-center overflow-hidden">
                <img
                  src={profilePicSrc}
                  className="size-4"
                  alt=""
                />
              </div>
            )}

            <div
              className={clsx('text-sm md:text-base flex flex-col', {
                'max-w-[75%] items-end':
                  !isInHumanHandoverApp && role === 'user',
                'w-full': !isInHumanHandoverApp && role === 'assistant',
                'max-w-[70%]': isInHumanHandoverApp,
                'items-end': isInHumanHandoverApp && role === 'assistant',
                'items-start': isInHumanHandoverApp && role === 'user',
              })}
            >
              <div className={`flex ${isInHumanHandoverApp ? 'flex-col' : 'items-center gap-size1'} ${isInHumanHandoverApp && role !== 'user' ? 'border border-grey-5 rounded-size2 p-size1 w-fit self-end' : ''}`}>
                {type === 'quick_response' && isInHumanHandoverApp && (
                  <span className="text-purple-300 text-2xs flex items-center gap-size0 rounded-size0 p-size0 bg-purple-10 w-max">
                    <QuickResponseIcon className="size-3" />
                    Quick Response
                  </span>
                )}
                  {!was_answered && role === 'user' && place === 'chat_record' && checkFeatureAvailability('unanswered_question_recognition') && <FlagIcon className="text-negative-200 size-5 flex-shrink-0"/>}
                <div
                  className={`rounded-size2 space-y-size3 w-fits 
                    ${
                    role === 'user' && (kb_id !== 'ca7ac107-9716-4ef3-a6b5-55ceab49f15a' && kb_id !== '86927718-7690-4c0c-a99d-8bc8afda0a4c' && !isInApp)
                      ? 'border px-3 py-2 whitespace-pre-wrap'
                      : ''
                  }
                  ${
                    role === 'user' && (kb_id === 'ca7ac107-9716-4ef3-a6b5-55ceab49f15a' || kb_id === '86927718-7690-4c0c-a99d-8bc8afda0a4c' || isInApp)
                      ? 'bg-[#6866F2] text-white px-3 py-2 rounded-[32px] r whitespace-pre-wrap'
                      : ''
                  }`}
                  style={{
                    color: role === 'user' && (kb_id === 'ca7ac107-9716-4ef3-a6b5-55ceab49f15a' || kb_id === '86927718-7690-4c0c-a99d-8bc8afda0a4c' || isInApp) ? undefined : `var(--dt-color-element-75)`,
                    borderColor: role === 'user' && (kb_id === 'ca7ac107-9716-4ef3-a6b5-55ceab49f15a' || kb_id === '86927718-7690-4c0c-a99d-8bc8afda0a4c' || isInApp) ? undefined : `var(--dt-color-element-5)`,
                    backgroundColor:
                      role === 'user' && (kb_id === 'ca7ac107-9716-4ef3-a6b5-55ceab49f15a' || kb_id === '86927718-7690-4c0c-a99d-8bc8afda0a4c' || isInApp)
                        ? undefined
                        : role === 'user'
                        ? `var(--dt-color-element-2)`
                        : 'transparent',
                    fontFamily: kb_id === 'ca7ac107-9716-4ef3-a6b5-55ceab49f15a' || kb_id === '86927718-7690-4c0c-a99d-8bc8afda0a4c' || isInApp ? '-apple-system, BlinkMacSystemFont, "San Francisco"' : undefined,
                    borderBottomRightRadius: '4px',
                  }}
                >
                  <div className={status !== 'loading' ? 'animate-content-appear' : 'hidden'}>
                    {children}
                  </div>

                  {/* Display images if present */}
                  {images && images.length > 0 && (
                    <div className={`flex flex-wrap gap-size1 mt-size1 ${status !== 'loading' ? 'animate-content-appear' : 'hidden'}`} style={{ animationDelay: '0.15s' }}>
                      {images.map((image, index) => (
                        <div
                          key={index}
                          className="relative w-28 h-28 rounded-size1 overflow-hidden cursor-pointer bg-grey-5"
                          onClick={() => setPreviewImage(image)}
                        >
                          <img
                            src={image}
                            alt={`Image ${index + 1}`}
                            className="w-full h-full object-contain"
                          />
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>

              {status === 'loading' && (
                <div
                  style={{
                    color: 'var(--dt-color-brand-100)',
                    backgroundColor: 'transparent',
                  }}
                >
                  <div className="flex items-center justify-start gap-size1 h-6 d-loader-write -ml-size0">
                    <DLoaderWriting />
                  </div>
                </div>
              )}

              {(role === 'user' || completed_date) && !hideFooter && (kb_id !== 'ca7ac107-9716-4ef3-a6b5-55ceab49f15a' && kb_id !== '86927718-7690-4c0c-a99d-8bc8afda0a4c' && !isInApp) && (
                <div className={clsx('flex gap-size1 items-center', {
                  'justify-end': isInHumanHandoverApp,
                  'justify-start': !isInHumanHandoverApp,
                })}>
                  <div
                    className={`flex gap-size0 mt-size0 text-xs h-[20px] items-center ${
                      role === 'assistant'
                        ? 'justify-start'
                        : 'justify-end mx-size2'
                    }`}
                    style={{ color: 'var(--dt-color-element-20)' }}
                  >
                    {isInHumanHandoverApp && completed_date && completed_date.isValid
                      ? completed_date.toLocaleString(DateTime.DATETIME_MED)
                      : ''}
                    {!isInHumanHandoverApp && <ConfirmIcon />}{' '}
                    {completed_date && !isInHumanHandoverApp && completed_date.isValid
                      ? completed_date.toLocaleString(DateTime.DATETIME_MED)
                      : !isInHumanHandoverApp 
                      ? 'Delivered'
                      : ''}
                  </div>
                  {place === 'chat_record' && (
                    <DButtonIcon
                      className={clsx({
                        'sources-icon': true,
                        'text-[var(--dt-color-element-20)]': true,
                        '!w-3 !h-[15px]': true,
                        'hover:bg-[var(--dt-color-element-2)]': true,
                        hidden: !showSources.includes(id),
                      })}
                      onClick={() => handleFooterButton('sources')}
                    >
                      {sourcesLoading.includes(id) ? (
                        <DSpinner />
                      ) : (
                        <FileIcon className="size-3" />
                      )}
                    </DButtonIcon>
                  )}
                </div>
              )}

              <div
                className={clsx('flex', {
                  'min-h-8':
                    role === 'assistant' &&
                    !completed_date &&
                    !hideFooter &&
                    type !== 'welcome_message' &&
                    !interactingWithLiveAgent &&
                    !pollResponse,  

                }, {
                  'min-h-2': role === 'assistant' && pollResponse
                })}
              >
                <DTransition
                  show={
                    role === 'assistant' &&
                    status === 'complete' &&
                    type !== 'welcome_message' &&
                    !completed_date &&
                    !hideFooter
                  }
                >
                  <div className="flex gap-0">
                    {!isDanteFaq && !isInApp && (
                      <DButtonIcon
                        disabled={readonly}
                        onClick={() => handleFooterButton('thumbs_up')}
                        className={clsx( 'w-[24px]' ,{
                          'thumbs-up-icon': true,
                          'text-[var(--dt-color-element-100)]':
                            reaction === 'thumbs_up',
                          'text-[var(--dt-color-element-20)]':
                            reaction !== 'thumbs_up',
                          '!w-[18px] !h-[28px]': true,
                          'hover:bg-[var(--dt-color-element-2)]': true,
                        })}
                      >
                        <ThumbUpIcon className="size-[18px]" />
                      </DButtonIcon>
                    )}
                    {!isDanteFaq && !isInApp && (
                      <DButtonIcon
                        disabled={readonly}
                        className={clsx({
                          'thumbs-down-icon': true,
                          'text-[var(--dt-color-element-100)]':
                            reaction === 'thumbs_down',
                          'text-[var(--dt-color-element-20)]':
                            reaction !== 'thumbs_down',
                          '!w-[28px] !h-[28px]': true,
                          'hover:bg-[var(--dt-color-element-2)]': true,
                        })}
                        onClick={() => handleFooterButton('thumbs_down')}
                      >
                        <ThumbDownIcon className="size-[18px]" />
                      </DButtonIcon>
                    )}
                    <DTransitionFast
                      show={
                        role === 'assistant' &&
                        status === 'complete' &&
                        type !== 'welcome_message' &&
                        !completed_date &&
                        !hideFooter
                      }
                    >
                      <DButtonIcon
                        disabled={readonly}
                        onClick={() => handleFooterButton('play_message')}
                        className={clsx({
                          'play-message-icon': true,
                          'text-[var(--dt-color-element-100)]':
                            reaction === 'play_message',
                          'text-[var(--dt-color-element-20)]':
                            reaction !== 'play_message',
                          '!w-[28px] !h-[28px]': true,
                          'hover:bg-[var(--dt-color-element-2)]': true,
                        })}
                      >
                        <SoundIcon className="size-4.5" />
                      </DButtonIcon>
                    </DTransitionFast>
                    {(isInApp || place === 'chat_record') && (
                      <DTransitionFast
                        show={
                          role === 'assistant' &&
                          status === 'complete' &&
                          type !== 'welcome_message' &&
                          !completed_date &&
                          !hideFooter &&
                          showSources.includes(id)
                        }
                      >
                        <DButtonIcon
                          className={clsx({
                            'sources-icon': true,
                            'text-[var(--dt-color-element-20)]': true,
                            '!w-[28px] !h-[28px]': true,
                            'hover:bg-[var(--dt-color-element-2)]': true,
                          })}
                          onClick={() => {
                            if(featureCheck('sources')) {
                              handleFooterButton('sources');
                            }
                          }}
                        >
                          {sourcesLoading.includes(id) ? (
                            <DSpinner className="size-5" />
                          ) : (
                            <FileIcon className="size-3" />
                          )}
                        </DButtonIcon>
                      </DTransitionFast>
                    )}
                  </div>
                </DTransition>
              </div>
            </div>
          </div>
        </div>
      ) : type === 'agent_connecting' ? (
        <div className="grow-0 flex justify-center gap-size1 items-center py-size2 text-orange-300 whitespace-nowrap w-full animate-fadeIn">
          <hr className="w-full border-t border-orange-10" />
          <div className="flex items-center gap-size1">
            <AgentRequestIcon />
            User requested an agent
          </div>
          <hr className="w-full border-t border-orange-10" />
        </div>
      )
      : (
        <div className="w-full flex gap-size2 items-center my-2 animate-fadeIn">
          <div className="h-px grow bg-grey-5" />
          <p className="text-sm md:text-base font-regular tracking-tight text-grey-20">
            {children}
          </p>
          <div className="h-px grow bg-grey-5" />
        </div>
      )}
    </article>
    </>
  );
};

export default DBubbleMessage;
