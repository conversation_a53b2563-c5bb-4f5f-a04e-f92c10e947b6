import React, { useState, useEffect } from 'react';
import DTransition from '../DTransition';
import DTransitionFast from '../DTransitionFast';
import DButtonIcon from '../DButtonIcon';
import SoundIcon from '../Icons/SoundIcon';
import FileIcon from '../Icons/FileIcon';

const IconTimingTest = () => {
  const [showMessage, setShowMessage] = useState(false);
  const [messageStatus, setMessageStatus] = useState('loading');
  const [startTime, setStartTime] = useState(null);
  const [iconShowTime, setIconShowTime] = useState(null);

  useEffect(() => {
    // Simulate message loading and completion
    const timer1 = setTimeout(() => {
      setShowMessage(true);
      setStartTime(Date.now());
    }, 500);

    const timer2 = setTimeout(() => {
      setMessageStatus('complete');
    }, 1000);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
    };
  }, []);

  const handleIconVisible = () => {
    if (startTime && !iconShowTime) {
      const delay = Date.now() - startTime;
      setIconShowTime(delay);
      console.log(`Icons appeared after: ${delay}ms`);
    }
  };

  const reset = () => {
    setShowMessage(false);
    setMessageStatus('loading');
    setStartTime(null);
    setIconShowTime(null);
    
    setTimeout(() => {
      setShowMessage(true);
      setStartTime(Date.now());
      setTimeout(() => setMessageStatus('complete'), 500);
    }, 100);
  };

  return (
    <div className="p-8 space-y-4">
      <h2 className="text-xl font-bold">Icon Timing Test</h2>
      
      <div className="space-y-2">
        <p>Message Status: <span className="font-mono">{messageStatus}</span></p>
        <p>Icons Delay: <span className="font-mono">{iconShowTime ? `${iconShowTime}ms` : 'Not shown yet'}</span></p>
      </div>

      <button 
        onClick={reset}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Reset Test
      </button>

      {/* Current Implementation (Nested Transitions) */}
      <div className="border p-4 rounded">
        <h3 className="font-semibold mb-2">Current (Nested Transitions - SLOW)</h3>
        <div className="bg-gray-100 p-3 rounded">
          <p>Assistant message content here...</p>
          
          <DTransition
            show={showMessage && messageStatus === 'complete'}
          >
            <div className="flex gap-0 mt-2">
              <DTransitionFast
                show={showMessage && messageStatus === 'complete'}
              >
                <DButtonIcon
                  onClick={handleIconVisible}
                  className="!w-[28px] !h-[28px] hover:bg-gray-200"
                >
                  <SoundIcon className="size-4.5" />
                </DButtonIcon>
              </DTransitionFast>
              
              <DTransitionFast
                show={showMessage && messageStatus === 'complete'}
              >
                <DButtonIcon className="!w-[28px] !h-[28px] hover:bg-gray-200">
                  <FileIcon className="size-3" />
                </DButtonIcon>
              </DTransitionFast>
            </div>
          </DTransition>
        </div>
      </div>

      {/* Fixed Implementation (Single Fast Transition) */}
      <div className="border p-4 rounded">
        <h3 className="font-semibold mb-2">Fixed (Single Fast Transition - FAST)</h3>
        <div className="bg-gray-100 p-3 rounded">
          <p>Assistant message content here...</p>
          
          <DTransitionFast
            show={showMessage && messageStatus === 'complete'}
          >
            <div className="flex gap-0 mt-2">
              <DButtonIcon className="!w-[28px] !h-[28px] hover:bg-gray-200">
                <SoundIcon className="size-4.5" />
              </DButtonIcon>
              
              <DButtonIcon className="!w-[28px] !h-[28px] hover:bg-gray-200">
                <FileIcon className="size-3" />
              </DButtonIcon>
            </div>
          </DTransitionFast>
        </div>
      </div>

      {/* No Transition (Instant) */}
      <div className="border p-4 rounded">
        <h3 className="font-semibold mb-2">No Transition (Instant)</h3>
        <div className="bg-gray-100 p-3 rounded">
          <p>Assistant message content here...</p>
          
          {showMessage && messageStatus === 'complete' && (
            <div className="flex gap-0 mt-2">
              <DButtonIcon className="!w-[28px] !h-[28px] hover:bg-gray-200">
                <SoundIcon className="size-4.5" />
              </DButtonIcon>
              
              <DButtonIcon className="!w-[28px] !h-[28px] hover:bg-gray-200">
                <FileIcon className="size-3" />
              </DButtonIcon>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default IconTimingTest;
