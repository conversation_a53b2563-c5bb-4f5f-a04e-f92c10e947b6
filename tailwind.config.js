import { breakpoints } from './src/constants';

/** @type {import('tailwindcss').Config} */
export const content = ['./*.html', './src/**/*.{js,ts,jsx,tsx}'];
/** @type {import('tailwindcss').Config.theme} */
export const theme = {
  extend: {
    // Custom typography settings
    fontSize: {
      'min-safe-input': 'clamp(16px, 1em, 100px) !important',
      '2xs': ['0.5em', { lineHeight: '1.95em' }], // font-size 8px
      xs: ['0.75em', { lineHeight: '1.95em' }], // font-size 12px
      sm: ['0.875em', { lineHeight: '1.4em' }], // font-size 14px
      base: ['1em', { lineHeight: '1.25em' }], // font-size 16px
      lg: ['1.125em', { lineHeight: '1.35em' }], // font-size 18px
      xl: ['1.5em', { lineHeight: '1em' }], // font-size 24px
      '2xl': ['2em', { lineHeight: '1em' }], // font-size 32px
      '3xl': ['3em', { lineHeight: '1em' }], // font-size 48px
      '4xl': ['4em', { lineHeight: '1em' }] // font-size 64px
    },
    screens: breakpoints,

    // Custom sizes for padding, margins, gaps, etc.
    spacing: {
      size0: '4px',
      size1: '8px',
      size2: '12px',
      size3: '16px',
      size4: '16px',
      size5: ['18px', { sm: '24px' }],
      size6: ['18px', { sm: '24px' }],
      size7: '24px',
      // size8: ['48px', { sm: '24px' }],
      size8: '48px'
    },
    fontWeight: {
      regular: 400,
      medium: 500,
      bold: 600
    },
    letterSpacing: {
      tight: '-0.02px',
      tighter: '-0.04px'
    },

    // Custom border radius
    borderRadius: {
      size0: '4px',
      size1: '8px',
      size2: '12px',
      size3: '16px',
      size4: '16px',
      size5: '24px',
      size6: '24px'
    },

    // Custom gaps
    gap: {
      size0: '4px',
      size1: '8px',
      size2: '12px',
      size3: '16px',
      size4: '16px',
      size5: '24px',
      size6: '24px',
      size7: '32px'
    },

    padding: {
      size0: '4px',
      size1: '8px',
      size2: '12px',
      size3: '16px',
      size4: '16px',
      size5: '24px',
      size6: '24px',
      size7: '32px'
    },

    // Custom colors
    colors: {
      purple: {
        5: 'rgba(var(--color-purple-opacity), 0.05)',
        10: 'rgba(var(--color-purple-opacity), 0.1)',
        20: 'rgba(var(--color-purple-opacity), 0.2)',
        50: 'rgba(var(--color-purple-opacity), 0.5)',
        100: 'rgba(var(--color-purple-opacity), 0.1)',
        200: 'rgba(var(--color-purple-200), 1)',
        300: 'rgba(var(--color-purple-300), 1)'
      },
      oklch: {
        100: 'oklch(var(--color-oklch-100))',
      },
      green: {
        5: 'rgba(var(--color-green-5), 0.05)',
        10: 'rgba(var(--color-green-5), 0.1)',
        20: 'rgba(var(--color-green-5), 0.2)',
        100: 'rgba(var(--color-green-100), 1)',
        200: 'rgba(var(--color-green-200), 1)',
        300: 'rgba(var(--color-green-300), 1)',
        400: 'rgba(var(--color-green-400), 1)',
        500: 'rgba(var(--color-green-500), 1)'
      },
      darkGreen: {
        10: 'rgba(var(--color-dark-green-300), 0.1)',
        300: 'rgba(var(--color-dark-green-300), 1)'
      },
      negative: {
        2: 'rgba(var(--color-negative-100), 0.02)',
        5: 'rgba(var(--color-negative-100), 0.05)',
        10: 'rgba(var(--color-negative-100), 0.1)',
        20: 'rgba(var(--color-negative-100), 0.2)',
        100: 'rgba(var(--color-negative-100), 1)',
        200: 'rgba(var(--color-negative-200), 1)'
      },
      orange: {
        5: 'rgba(var(--color-orange-5), 0.05)',
        10: 'rgba(var(--color-orange-5), 0.1)',
        20: 'rgba(var(--color-orange-5), 0.2)',
        100: 'rgba(var(--color-orange-300), 1)',
        200: 'rgba(var(--color-orange-200), 1)',
        300: 'rgba(var(--color-orange-300), 1)'
      },
      grey: {
        1: 'rgba(var(--color-grey-opacity), 0.01)',
        2: 'rgba(var(--color-grey-opacity), 0.02)',
        5: 'rgba(var(--color-grey-opacity), 0.05)',
        10: 'rgba(var(--color-grey-opacity), 0.1)',
        20: 'rgba(var(--color-grey-opacity), 0.2)',
        30: 'rgba(var(--color-grey-opacity), 0.3)',
        50: 'rgba(var(--color-grey-opacity), 0.5)',
        75: 'rgba(var(--color-grey-opacity), 0.75)',
        100: 'rgba(var(--color-grey-100), 1)',
        300: 'rgba(var(--color-grey-opacity), 1)',
        400: 'rgba(var(--color-black), 1)'
      },
      yellow: {
        300: 'rgba(var(--color-yellow-300), 1)'
      },
      black: 'rgba(var(--color-black), var(--color-black-opacity))',
      white: 'rgba(var(--color-white), 1)'
    },

    boxShadow: {
      sm: '0px 10px 20px 0px #0000001A',
      md: '0px 0px 16px 4px #00000005',
      purple: '31px 114px 33px -10px rgba(167, 155, 254, 0.1)'
    },
    transitionTimingFunction: {
      'ease-out-smooth': 'cubic-bezier(0.25, 0.8, 0.25, 1)',
    },
    transitionDuration: {
      '400': '400ms',
      '600': '600ms',
    },

    // Background gradients
    backgroundImage: {
      'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
    },

    // Custom animations
    keyframes: {
      fadeIn: {
        '0%': { opacity: '0', transform: 'scale(0.98)' },
        '100%': { opacity: '1', transform: 'scale(1)' }
      },
      fadeInUp: {
        '0%': { opacity: '0', transform: 'translateY(10px)' },
        '100%': { opacity: '1', transform: 'translateY(0)' }
      },
      pulseSubtle: {
        '0%, 100%': { opacity: '1', transform: 'scale(1)' },
        '50%': { opacity: '0.85', transform: 'scale(0.95)' }
      },
      shimmer: {
        '0%': { transform: 'translateX(-100%)' },
        '100%': { transform: 'translateX(100%)' }
      },
    },
    animation: {
      fadeIn: 'fadeIn 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
      fadeInUp: 'fadeInUp 0.5s cubic-bezier(0.25, 0.8, 0.25, 1)',
      fadeInUpDelayed1: 'fadeInUp 0.5s cubic-bezier(0.25, 0.8, 0.25, 1) 0.1s both',
      fadeInUpDelayed2: 'fadeInUp 0.5s cubic-bezier(0.25, 0.8, 0.25, 1) 0.2s both',
      fadeInUpDelayed3: 'fadeInUp 0.5s cubic-bezier(0.25, 0.8, 0.25, 1) 0.3s both',
      'pulse-subtle': 'pulseSubtle 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      'shimmer': 'shimmer 2s infinite linear',
       'fadeInFast': 'fadeIn 0.10s cubic-bezier(0.25, 0.8, 0.25, 1) 0.025s both',
      'fadeInIconFast': 'fadeInUp 0.2s cubic-bezier(0.25, 0.8, 0.25, 1) 0.05s both',
    },

  }
};
export const plugins = [];
export const darkMode = ['class', '[data-mode="dark"]'];
